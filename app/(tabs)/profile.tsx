import React from 'react';
import { StyleSheet, View, Text, Image, ScrollView, TouchableOpacity } from 'react-native';
import { Stack, router } from 'expo-router';
import { Settings, LogOut, Award, Heart, Camera, Leaf, FileText, Shield } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useGarden } from '@/hooks/useGardenStore';
import { useFavorites } from '@/hooks/useFavoritesStore';
import { useAuth } from '@/hooks/useAuth';

export default function ProfileScreen() {
  const { plants } = useGarden();
  const { favorites } = useFavorites();
  const { user, signOut } = useAuth();
  
  // User data from auth
  const userData = {
    name: user?.user_metadata?.full_name || 'Plant Lover',
    email: user?.email || '<EMAIL>',
    avatar: user?.user_metadata?.avatar_url || 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=776&q=80',
    joinDate: user?.created_at ? new Date(user.created_at) : new Date(),
    identifications: 24, // This would come from your app's data
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };
  
  return (
    <ScrollView style={styles.container} testID="profile-screen">
      <Stack.Screen 
        options={{ 
          title: 'Profile',
          headerRight: () => (
            <TouchableOpacity style={styles.headerButton}>
              <Settings size={24} color={Colors.text} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <View style={styles.header}>
        <Image source={{ uri: userData.avatar }} style={styles.avatar} />
        <Text style={styles.name}>{userData.name}</Text>
        <Text style={styles.email}>{userData.email}</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{plants.length}</Text>
            <Text style={styles.statLabel}>Plants</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{favorites.length}</Text>
            <Text style={styles.statLabel}>Favorites</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{userData.identifications}</Text>
            <Text style={styles.statLabel}>Identified</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Achievements</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.achievementsContainer}>
          <Card style={styles.achievementCard}>
            <View style={[styles.achievementIcon, { backgroundColor: Colors.primary }]}>
              <Camera size={24} color={Colors.background} />
            </View>
            <Text style={styles.achievementTitle}>First Scan</Text>
            <Text style={styles.achievementDesc}>Identified your first plant</Text>
          </Card>
          
          <Card style={styles.achievementCard}>
            <View style={[styles.achievementIcon, { backgroundColor: Colors.accent1 }]}>
              <Leaf size={24} color={Colors.background} />
            </View>
            <Text style={styles.achievementTitle}>Plant Parent</Text>
            <Text style={styles.achievementDesc}>Added 5 plants to your garden</Text>
          </Card>
          
          <Card style={StyleSheet.flatten([styles.achievementCard, styles.lockedAchievement])}>
            <View style={[styles.achievementIcon, { backgroundColor: Colors.textMuted }]}>
              <Award size={24} color={Colors.background} />
            </View>
            <Text style={[styles.achievementTitle, styles.lockedText]}>Expert</Text>
            <Text style={[styles.achievementDesc, styles.lockedText]}>Identify 50 plants</Text>
          </Card>
        </ScrollView>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account</Text>
        <Card style={styles.accountCard}>
          <TouchableOpacity style={styles.accountOption}>
            <Heart size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Favorites</Text>
          </TouchableOpacity>
          
          <View style={styles.accountDivider} />
          
          <TouchableOpacity style={styles.accountOption}>
            <Settings size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Settings</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <TouchableOpacity
            style={styles.accountOption}
            onPress={() => router.push('/legal/terms')}
          >
            <FileText size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Terms of Service</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <TouchableOpacity
            style={styles.accountOption}
            onPress={() => router.push('/legal/privacy')}
          >
            <Shield size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Privacy Policy</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <TouchableOpacity style={styles.accountOption} onPress={handleSignOut}>
            <LogOut size={20} color={Colors.error} style={styles.accountIcon} />
            <Text style={[styles.accountOptionText, { color: Colors.error }]}>Sign Out</Text>
          </TouchableOpacity>
        </Card>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Premium Features</Text>
        <Card style={styles.premiumCard}>
          <Text style={styles.premiumTitle}>Upgrade to PlantIDGuide Pro</Text>
          <Text style={styles.premiumDesc}>
            Get unlimited plant identifications, advanced care guides, and expert advice
          </Text>
          <Button 
            title="Upgrade Now" 
            style={styles.upgradeButton}
            onPress={() => {}}
          />
        </Card>
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>PlantIDGuide v1.0.0</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerButton: {
    padding: 8,
    marginRight: 8,
  },
  header: {
    alignItems: 'center',
    padding: 20,
    paddingBottom: 0,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    color: Colors.textLight,
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.textLight,
  },
  statDivider: {
    width: 1,
    height: '100%',
    backgroundColor: Colors.border,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  achievementsContainer: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  achievementCard: {
    width: 150,
    marginRight: 16,
    alignItems: 'center',
    padding: 16,
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  achievementDesc: {
    fontSize: 12,
    color: Colors.textLight,
    textAlign: 'center',
  },
  lockedAchievement: {
    opacity: 0.7,
  },
  lockedText: {
    color: Colors.textMuted,
  },
  accountCard: {
    padding: 0,
  },
  accountOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  accountIcon: {
    marginRight: 16,
  },
  accountOptionText: {
    fontSize: 16,
    color: Colors.text,
  },
  accountDivider: {
    height: 1,
    backgroundColor: Colors.border,
    width: '100%',
  },
  premiumCard: {
    backgroundColor: Colors.secondary,
  },
  premiumTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  premiumDesc: {
    fontSize: 14,
    color: Colors.textLight,
    marginBottom: 16,
    lineHeight: 20,
  },
  upgradeButton: {
    backgroundColor: Colors.primaryDark,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: Colors.textMuted,
  },
});