import React, { useState } from 'react';
import { StyleSheet, View, Text, FlatList, ActivityIndicator } from 'react-native';
import { Stack } from 'expo-router';
import { Plus } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { GardenPlantCard } from '@/components/garden/GardenPlantCard';
import { SearchBar } from '@/components/ui/SearchBar';
import { Button } from '@/components/ui/Button';
import { useGarden } from '@/hooks/useGardenStore';
import { GardenPlant } from '@/types/plant';

export default function GardenScreen() {
  const { plants, isLoading, waterPlant } = useGarden();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredPlants = plants.filter(
    (plant) =>
      plant.commonName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plant.scientificName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (plant.nickname && plant.nickname.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const renderItem = ({ item }: { item: GardenPlant }) => (
    <GardenPlantCard
      plant={item}
      onPress={() => console.log('View plant details', item.id)}
      onWater={() => waterPlant(item.id)}
    />
  );

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>Your garden is empty</Text>
      <Text style={styles.emptyText}>
        Start by identifying plants and adding them to your garden
      </Text>
      <Button
        title="Identify a Plant"
        onPress={() => {}}
        style={styles.identifyButton}
      />
    </View>
  );

  return (
    <View style={styles.container} testID="garden-screen">
      <Stack.Screen
        options={{
          title: 'My Garden',
          headerRight: () => (
            <Button
              title="Add"
              variant="text"
              style={styles.addButton}
              textStyle={styles.addButtonText}
              onPress={() => {}}
            />
          ),
        }}
      />

      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search your garden..."
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredPlants}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
          showsVerticalScrollIndicator={false}
          testID="garden-plant-list"
        />
      )}

      <View style={styles.fabContainer}>
        <Button
          title="Add Plant"
          style={styles.fab}
          textStyle={styles.fabText}
          onPress={() => {}}
          testID="add-plant-fab"
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  addButton: {
    marginRight: 8,
  },
  addButtonText: {
    fontSize: 16,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    marginBottom: 24,
  },
  identifyButton: {
    width: 200,
  },
  fabContainer: {
    position: 'absolute',
    bottom: 24,
    right: 24,
  },
  fab: {
    borderRadius: 28,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  fabText: {
    marginLeft: 8,
  },
});