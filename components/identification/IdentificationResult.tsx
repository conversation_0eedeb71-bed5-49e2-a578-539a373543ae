import React from 'react';
import { StyleSheet, Text, View, Image, ScrollView } from 'react-native';
import { Check, AlertCircle } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { IdentificationResult } from '@/types/plant';
import { Button } from '@/components/ui/Button';
import { CareIndicator } from '@/components/ui/CareIndicator';
import { Card } from '@/components/ui/Card';

interface IdentificationResultViewProps {
  result: IdentificationResult;
  onAddToGarden: () => void;
  onNewScan: () => void;
}

export const IdentificationResultView: React.FC<IdentificationResultViewProps> = ({
  result,
  onAddToGarden,
  onNewScan,
}) => {
  const { plant, confidence, imageUri, diagnosis, treatment } = result;
  const isHighConfidence = confidence >= 0.9;

  return (
    <ScrollView style={styles.container} testID="identification-result">
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUri }} style={styles.image} />
        <View style={[styles.confidenceBadge, isHighConfidence ? styles.highConfidence : styles.lowConfidence]}>
          {isHighConfidence ? (
            <Check size={16} color={Colors.background} />
          ) : (
            <AlertCircle size={16} color={Colors.background} />
          )}
          <Text style={styles.confidenceText}>{Math.round(confidence * 100)}% Match</Text>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.commonName}>{plant.commonName}</Text>
        <Text style={styles.scientificName}>{plant.scientificName}</Text>
        
        <Text style={styles.description}>{plant.description}</Text>
        
        <Text style={styles.sectionTitle}>Care Instructions</Text>
        <Card style={styles.careCard}>
          <CareIndicator type="light" level={plant.careInstructions.light} />
          <CareIndicator type="water" level={plant.careInstructions.water} />
          <CareIndicator type="humidity" level={plant.careInstructions.humidity} />
          
          <View style={styles.careDetail}>
            <Text style={styles.careLabel}>Temperature:</Text>
            <Text style={styles.careValue}>
              {plant.careInstructions.temperature.min}° - {plant.careInstructions.temperature.max}°{plant.careInstructions.temperature.unit}
            </Text>
          </View>
          
          <View style={styles.careDetail}>
            <Text style={styles.careLabel}>Soil:</Text>
            <Text style={styles.careValue}>{plant.careInstructions.soil}</Text>
          </View>
          
          <View style={styles.careDetail}>
            <Text style={styles.careLabel}>Fertilizer:</Text>
            <Text style={styles.careValue}>{plant.careInstructions.fertilizer}</Text>
          </View>
          
          <View style={styles.careDetail}>
            <Text style={styles.careLabel}>Toxicity:</Text>
            <Text style={styles.careValue}>
              {plant.careInstructions.toxicity.charAt(0).toUpperCase() + plant.careInstructions.toxicity.slice(1)}
            </Text>
          </View>
        </Card>

        {diagnosis && (
          <>
            <Text style={styles.sectionTitle}>Health Diagnosis</Text>
            <Card style={styles.diagnosisCard}>
              <Text style={styles.diagnosisText}>{diagnosis}</Text>
            </Card>
          </>
        )}

        {treatment && (
          <>
            <Text style={styles.sectionTitle}>Treatment Recommendations</Text>
            <Card style={styles.treatmentCard}>
              <Text style={styles.treatmentText}>{treatment}</Text>
            </Card>
          </>
        )}

        <View style={styles.actions}>
          <Button 
            title="Add to My Garden" 
            onPress={onAddToGarden} 
            style={styles.addButton}
            testID="add-to-garden-button"
          />
          <Button 
            title="New Scan" 
            variant="outline" 
            onPress={onNewScan} 
            style={styles.newScanButton}
            testID="new-scan-button"
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 300,
  },
  confidenceBadge: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  highConfidence: {
    backgroundColor: Colors.primary,
  },
  lowConfidence: {
    backgroundColor: Colors.error,
  },
  confidenceText: {
    color: Colors.background,
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 4,
  },
  content: {
    padding: 20,
  },
  commonName: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 16,
    fontStyle: 'italic',
    color: Colors.textLight,
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  careCard: {
    marginBottom: 24,
  },
  careDetail: {
    flexDirection: 'row',
    marginVertical: 4,
    paddingLeft: 44,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    width: 100,
  },
  careValue: {
    fontSize: 14,
    color: Colors.textLight,
    flex: 1,
  },
  actions: {
    marginTop: 8,
  },
  addButton: {
    marginBottom: 12,
  },
  newScanButton: {
    marginBottom: 24,
  },
  diagnosisCard: {
    marginBottom: 24,
    backgroundColor: '#FFF3CD',
    borderColor: '#FFEAA7',
  },
  diagnosisText: {
    fontSize: 14,
    color: '#856404',
    lineHeight: 20,
  },
  treatmentCard: {
    marginBottom: 24,
    backgroundColor: '#D1ECF1',
    borderColor: '#BEE5EB',
  },
  treatmentText: {
    fontSize: 14,
    color: '#0C5460',
    lineHeight: 20,
  },
});