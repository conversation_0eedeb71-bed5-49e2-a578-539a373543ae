import React from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import { Droplets } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { GardenPlant } from '@/types/plant';
import { Card } from '@/components/ui/Card';

interface GardenPlantCardProps {
  plant: GardenPlant;
  onPress: () => void;
  onWater?: () => void;
}

export const GardenPlantCard: React.FC<GardenPlantCardProps> = ({
  plant,
  onPress,
  onWater,
}) => {
  const daysSinceWatered = plant.lastWatered
    ? Math.floor((Date.now() - plant.lastWatered.getTime()) / (1000 * 60 * 60 * 24))
    : null;

  const getStatusColor = () => {
    switch (plant.healthStatus) {
      case 'healthy':
        return Colors.success;
      case 'needs-attention':
        return Colors.accent1;
      case 'unhealthy':
        return Colors.error;
      default:
        return Colors.textMuted;
    }
  };

  return (
    <Card onPress={onPress} style={styles.container} testID={`garden-plant-${plant.id}`}>
      <View style={styles.imageContainer}>
        <Image source={{ uri: plant.imageUrl }} style={styles.image} />
        <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
      </View>
      <View style={styles.content}>
        <View>
          <Text style={styles.nickname}>{plant.nickname || plant.commonName}</Text>
          <Text style={styles.scientificName}>{plant.scientificName}</Text>
        </View>
        
        <View style={styles.waterInfo}>
          {daysSinceWatered !== null && (
            <Text style={styles.waterText}>
              {daysSinceWatered === 0
                ? 'Watered today'
                : daysSinceWatered === 1
                ? 'Watered yesterday'
                : `Watered ${daysSinceWatered} days ago`}
            </Text>
          )}
          
          {onWater && (
            <TouchableOpacity 
              style={styles.waterButton} 
              onPress={onWater}
              testID={`water-button-${plant.id}`}
            >
              <Droplets size={20} color={Colors.primary} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 12,
    marginVertical: 8,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  statusIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: Colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  nickname: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  scientificName: {
    fontSize: 12,
    fontStyle: 'italic',
    color: Colors.textLight,
  },
  waterInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  waterText: {
    fontSize: 12,
    color: Colors.textMuted,
  },
  waterButton: {
    padding: 8,
    backgroundColor: Colors.secondary,
    borderRadius: 20,
  },
});