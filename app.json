{"expo": {"name": "PlantIDGuide: Plant Identifier", "slug": "plantidguide-plant-identifier", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.plantidguide", "infoPlist": {"NSCameraUsageDescription": "Allow $(PRODUCT_NAME) to access your camera", "NSMicrophoneUsageDescription": "Allow $(PRODUCT_NAME) to access your microphone", "NSPhotoLibraryUsageDescription": "Allow $(PRODUCT_NAME) to access your photos"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.plantidguide", "permissions": ["android.permission.VIBRATE", "CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.com/"}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}]], "experiments": {"typedRoutes": true}, "extra": {"supabaseUrl": "https://zlivouxymzpbyoxnwxrp.supabase.co", "supabaseAnonKey": "sb_publishable_ABgFmonyDTQFCL9IiD9Pzw_H7-dr7yC", "openrouterApiKey": "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"}}}