import Constants from 'expo-constants';

// Get OpenRouter API key from environment variables
const openrouterApiKey = Constants.expoConfig?.extra?.openrouterApiKey || process.env.OPENROUTER_API_KEY;

if (!openrouterApiKey) {
  console.warn('OpenRouter API key not found in environment variables');
}

// API Configuration
export const API_CONFIG = {
  OPENROUTER_API_KEY: openrouterApiKey,
  OPENROUTER_BASE_URL: 'https://openrouter.ai/api/v1',
  MODEL: 'google/gemini-2.5-flash-lite',
};
