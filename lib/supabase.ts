import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl || process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey || process.env.EXPO_PUBLIC_SUPABASE_KEY;

console.log('Supabase config:', {
  url: supabaseUrl ? 'loaded' : 'missing',
  key: supabaseAnonKey ? 'loaded' : 'missing',
  source: Constants.expoConfig?.extra?.supabaseUrl ? 'expo-config' : 'env-vars'
});

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase credentials missing:', { supabaseUrl, supabaseAnonKey });
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true, // Enable for OAuth redirects on web
    flowType: 'pkce', // Use PKCE flow for better security
  },
});
