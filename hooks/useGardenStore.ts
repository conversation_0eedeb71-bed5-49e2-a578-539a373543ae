import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import createContextHook from '@nkzw/create-context-hook';
import { GardenPlant, Plant } from '@/types/plant';
import { gardenPlants } from '@/mocks/plants';

const GARDEN_STORAGE_KEY = 'bloomsnap_garden';

export const [GardenProvider, useGarden] = createContextHook(() => {
  const [plants, setPlants] = useState<GardenPlant[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load garden plants from storage
  useEffect(() => {
    const loadGarden = async () => {
      try {
        const storedGarden = await AsyncStorage.getItem(GARDEN_STORAGE_KEY);
        if (storedGarden) {
          const parsedGarden = JSON.parse(storedGarden);
          // Convert string dates back to Date objects
          const plantsWithDates = parsedGarden.map((plant: any) => ({
            ...plant,
            addedDate: new Date(plant.addedDate),
            lastWatered: plant.lastWatered ? new Date(plant.lastWatered) : undefined,
            lastFertilized: plant.lastFertilized ? new Date(plant.lastFertilized) : undefined,
          }));
          setPlants(plantsWithDates);
        } else {
          // Use mock data for initial state
          setPlants(gardenPlants);
        }
      } catch (error) {
        console.error('Error loading garden:', error);
        setPlants(gardenPlants);
      } finally {
        setIsLoading(false);
      }
    };

    loadGarden();
  }, []);

  // Save garden plants to storage whenever it changes
  useEffect(() => {
    const saveGarden = async () => {
      try {
        await AsyncStorage.setItem(GARDEN_STORAGE_KEY, JSON.stringify(plants));
      } catch (error) {
        console.error('Error saving garden:', error);
      }
    };

    if (!isLoading) {
      saveGarden();
    }
  }, [plants, isLoading]);

  const addPlant = (plant: Plant, nickname?: string) => {
    const gardenPlant: GardenPlant = {
      ...plant,
      addedDate: new Date(),
      nickname: nickname || plant.commonName,
      healthStatus: 'healthy',
    };
    
    setPlants((current) => [...current, gardenPlant]);
  };

  const removePlant = (plantId: string) => {
    setPlants((current) => current.filter((plant) => plant.id !== plantId));
  };

  const waterPlant = (plantId: string) => {
    setPlants((current) =>
      current.map((plant) =>
        plant.id === plantId
          ? { ...plant, lastWatered: new Date() }
          : plant
      )
    );
  };

  const updatePlant = (updatedPlant: GardenPlant) => {
    setPlants((current) =>
      current.map((plant) =>
        plant.id === updatedPlant.id ? updatedPlant : plant
      )
    );
  };

  return {
    plants,
    isLoading,
    addPlant,
    removePlant,
    waterPlant,
    updatePlant,
  };
});